<template>
  <div class="medicine-config">
    <OgwSearch
      :fields="searchFields"
      v-model="searchForm"
      @search="handleSearch"
      @reset="handleReset"
    ></OgwSearch>

    <div class="table-container">
      <div class="table-actions">
        <el-button type="primary" size="mini" @click="addInfo">新增</el-button>
        <el-button type="primary" size="mini" @click="saveInfo">保存</el-button>
      </div>
      <OgwTable
        :loading="loading"
        :columns="columns"
        :data="tableData"
        :merge-keys="['productionName']"
        :show-actions="false"
        @cell-change="handleChange"
      >
        <template #instruction="{ row, $index }">
          <el-button
            v-if="!row.id"
            type="text"
            size="mini"
            @click="deleteRow(row, $index)"
          >
            删除</el-button
          >
          <el-button
            v-if="row.id"
            type="text"
            size="mini"
            @click="uploadRow(row)"
          >
            上传</el-button
          >
          <el-button
            v-if="row.id"
            type="text"
            size="mini"
            @click="checkInfo(row)"
            >查看({{
              row.fileInfo ? row.fileInfo.split(",").length : 0
            }})</el-button
          ></template
        >
      </OgwTable>
    </div>
    <FileUpload
      :visible.sync="showUpload"
      :upload-url="`${prefix}/chemicalConfig/upload`"
      :file-types="['.pdf', '.png', '.jpg']"
      :maxSizeMB="10"
      :fileData="fileData"
      @upload-success="uploadSuccess"
    />
  </div>
</template>
<script>
import OgwSearch from "@/components/comTable/OgwSearch.vue";
import OgwTable from "@/components/comTable/OgwTable.vue";
import {
  getTableList,
  updateMedicineConfig,
  getMedicineInfo,
} from "@/api/ogwWatch/medicineConfig.js";
import { getProd } from "@/api/common.js";
export default {
  name: "medicineConfig",
  components: {
    OgwSearch,
    OgwTable,
  },
  mounted() {
    this.getChemicalNameList();
    this.getProdList();
    this.getTableListInfo();
  },
  watch: {
    searchForm: {
      handler(val) {
        this.deviceOptions = this.orgList.map((item) => {
          if (item.orgId === val.orgId) {
            return item.children;
          }
        });
        this.searchFields[1].options = this.deviceOptions.flat(Infinity);
      },
      deep: true,
    },
  },
  computed: {
    searchFields() {
      return [
        {
          label: "组织机构:",
          prop: "orgId",
          type: "select",
          options: this.orgList.map((item) => {
            return {
              label: item.orgName,
              value: item.orgId,
            };
          }),
        },
        {
          label: "平台名称:",
          prop: "deviceNameCode",
          type: "select",
          options: [],
        },
      ];
    },
  },
  data() {
    return {
      deviceOptions: [],
      chemicalNameOptions: [],
      chemicalDataMap: {}, // 存储完整的药剂数据映射，用于联动
      loading: false,
      showUpload: false,
      hzNo: "",
      orgList: [],
      prefix: process.env.VUE_APP_PREFIX,
      searchForm: {
        orgId: "",
        deviceNameCode: "",
      },
      columns: [
        {
          label: "平台名称",
          prop: "productionName",
          editable: false,
          options: [],
        },
        {
          label: "药剂名称",
          prop: "chemicalId",
          editable: true,
          options: [],
        },
        { label: "药剂型号", prop: "chemicalType" },
        { label: "库存编码", prop: "materialCode", editable: true },
        { label: "推荐加注浓度(ppm)", prop: "reConcentration", editable: true },
        {
          label: "用途",
          prop: "purpose",
          editable: true,
          options: [
            { label: "原油处理", value: "4304030400" },
            { label: "污水处理", value: "4304030100" },
            { label: "注水处理", value: "4304030200" },
            { label: "天然气处理", value: "4304030300" },
            { label: "其他化学药品", value: "4304030500" },
          ],
        },
        {
          label: "费用关注度",
          prop: "attention",
          editable: true,
          options: [
            { label: "重点关注", value: 1 },
            { label: "一般关注", value: 2 },
          ],
        },
        { label: "加注浓度(ppm)", prop: "concentration", editable: true },
        {
          label: "密度(kg/m³)",
          prop: "density",
          editable: true,
          validation: {
            type: "decimal",
            required: true,
            precision: 2,
            errorMessage: "请输入数字",
          },
        },
        { label: "药剂说明书", prop: "instruction" },
      ],
      tableData: [],
      updateData: [], //表格更新的数据
      fileData: {},
    };
  },
  methods: {
    handleSearch(value) {
      this.hzNo = value.deviceNameCode;
      this.getTableListInfo();
    },
    handleReset(value) {
      // 重置搜索表单
      this.searchForm = {
        orgId: "",
        deviceNameCode: "",
      };
      this.hzNo = "";
      this.getTableListInfo();
    },
    uploadRow(row) {
      this.showUpload = true;
      this.fileData = { id: row.id };
    },
    checkInfo(row) {
      const fileIds = row.fileInfo || null;

      this.$router.push({
        name: "opinionsPre",
        params: { fileIds },
      });
    },
    deleteRow(data, index) {
      this.tableData.splice(index, 1);
      this.updateData = this.updateData.filter((item) => item !== index);
    },
    addInfo() {
      this.columns[0].editable = true;
      this.tableData.unshift({
        productionName: "",
        chemicalId: "", // 药剂名称ID
        chemicalType: "", // 药剂型号
        materialCode: "", // 库存编码
        reConcentration: "",
        purpose: "",
        attention: "",
        concentration: "",
        density: "",
        instruction: "",
      });
    },
    async getTableListInfo() {
      this.loading = true;
      try {
        const res = await getTableList(this.hzNo || "");
        if (res.code === 200) {
          this.tableData = res.data;
        } else {
          this.tableData = [];
        }
      } catch (error) {
        console.log("error", error);
      } finally {
        this.loading = false;
      }
    },
    async saveInfo() {
      // set集合转成数组
      const targetArr = Array.from(new Set(this.updateData));

      const targetObj = targetArr.map((item) => this.tableData[item]);

      const res = await updateMedicineConfig(targetObj);
      if (res.code === 200) {
        this.$message.success("保存成功");
        this.getTableListInfo();
      }
    },
    handleChange(row) {
      this.updateData.push(row.index);

      // 实现药剂名称联动功能
      if (row.prop === "chemicalId") {
        this.handleChemicalChange(row);
      }

      if (row.prop === "productionName") {
        const hzNo = this.deviceOptions.find(
          (item) => item.label === row.value
        ).hzNo;
        this.$set(this.tableData[row.index], "hzNo", hzNo);
      }
    },

    // 处理药剂名称变化的联动逻辑
    handleChemicalChange({ row, value, index }) {
      // 根据选择的药剂ID查找对应的完整信息
      const selectedChemical = this.chemicalDataMap[value];

      if (selectedChemical) {
        // 自动填充药剂型号和库存编码
        this.$set(
          this.tableData[index],
          "chemicalType",
          selectedChemical.chemicalType || ""
        );
        // 强制更新视图
        this.$forceUpdate();
      }
    },
    uploadSuccess() {
      this.showUpload = false;
      this.getTableListInfo();
    },
    async getProdList() {
      const res = await getProd();
      if (res.code === 200) {
        this.orgList = res.data.map((item) => ({
          orgId: item.orgId,
          orgName: item.orgName,
          children: item.children.map((child) => ({
            value: child.hzNo,
            label: child.name,
          })),
        }));
        this.deviceOptions = this.orgList
          .map((item) => {
            return item.children.map((c) => {
              return {
                label: c.label,
                value: c.label,
                hzNo: c.value,
              };
            });
          })
          .flat();
          this.searchForm.orgId = this.orgList[0].orgId;
        this.columns[0].options = this.deviceOptions;
      }
    },
    async getChemicalNameList() {
      const res = await getMedicineInfo();
      if (res.code === 200) {
        // 构建药剂选项数据
        this.chemicalNameOptions = res.data.map((item) => {
          return {
            label: item.chemicalName,
            value: item.id,
          };
        });

        // 构建药剂数据映射，用于联动功能
        // 假设API返回的数据结构包含：id, chemicalName, chemicalType, materialCode
        this.chemicalDataMap = {};
        res.data.forEach((item) => {
          this.chemicalDataMap[item.id] = {
            id: item.id,
            chemicalName: item.chemicalName,
            chemicalType: item.chemicalType || item.model || "", // 药剂型号
          };
        });
        this.columns[1].options = this.chemicalNameOptions;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.medicine-config {
  padding: 20px;
}
.table-container {
  margin-top: 20px;
  .table-actions {
    margin-bottom: 10px;
    text-align: right;
  }
}

[data-theme="dark"] .medicine-config {
  background: #162549;
}

[data-theme="tint"] .medicine-config {
  background: #fff;
}
</style>
